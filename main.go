package main

import (
	_ "ai-gateway/boot"
	_ "ai-gateway/internal/logic"
	_ "ai-gateway/internal/packed"

	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gbuild"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/glog"

	"ai-gateway/internal/cmd"
)

func main() {
	g.Log().SetHeaderPrint(false)
	buildInfo := gbuild.Info()
	g.Log().Infof(context.TODO(), "AI-Gateway Version: %s , Build Time: %s", buildInfo.Version, buildInfo.Time)

	g.Log().SetHeaderPrint(true)
	g.Log().SetFlags(glog.F_FILE_SHORT | glog.F_TIME_STD | glog.F_CALLER_FN)
	cmd.Main.Run(gctx.GetInitCtx())
}
