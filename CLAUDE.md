# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build & Run
```bash
make build              # Build binary using GoFrame CLI with configuration from hack/config.yaml
./build.sh <version> <arch> <os>  # Custom build script with version info
./bin/ai-gateway        # Run the built binary
```

### Code Generation (GoFrame)
```bash
make ctrl               # Generate controllers from API definitions  
make service            # Generate service interfaces from logic implementations
make dao                # Generate database access objects
make pb                 # Generate protobuf Go files
make enums              # Generate enums from current Go files
```

### Docker & Deployment
```bash
make image              # Build Docker image with auto-generated tag
make image.push         # Build and push Docker image  
make deploy             # Deploy to Kubernetes using Kustomize
```

### Framework Updates
```bash
make up                 # Update GoFrame and CLI to latest stable version
```

## Architecture Overview

This is an **AI Gateway Service** built with GoFrame v2.9.0 that routes requests to backend AI microservices. It follows GoFrame's layered architecture pattern:

- **Controllers** (`/internal/controller/`): HTTP request handling
- **Logic** (`/internal/logic/`): Business logic implementation  
- **Service** (`/internal/service/`): Interface definitions
- **Model** (`/internal/model/`): Data structures and validation
- **DAO** (`/internal/dao/`): Data access layer (currently unused)

### Key Components

**Main Gateway Controller** (`/internal/controller/svc/svc_v1_svc.go`):
- Handles `/svc/*` routes for service proxying
- Validates tenant authentication via auth services
- Parses URIs to extract service names and actions
- Forwards requests to target microservices based on configuration

**Service Discovery Logic** (`/internal/logic/svc/svc.go`):
- Manages service-to-URL mapping via Nacos configuration
- Handles tenant validation against authentication endpoints
- Configures HTTP clients for backend service communication

**Bootstrap Process** (`/boot/boot.go`, `/main.go`):
- Initializes Nacos for service discovery and configuration management
- Sets up centralized configuration with hot-reloading capabilities
- Registers service with discovery system

### Request Flow
1. Client sends request to `/svc/{service}/{action}`
2. Gateway parses URI to extract service name and action
3. Service configuration lookup in Nacos
4. Optional tenant authentication check
5. Request forwarded to target microservice
6. Response proxied back to client

## Configuration

- **Nacos Integration**: Centralized configuration and service discovery
- **Environment Support**: `dev` and `pro` environments with separate configs
- **Runtime Configuration**: Stored in `/cache/config/` directory
- **Build Configuration**: GoFrame CLI config in `/hack/config.yaml`

## Key Development Patterns

**Adding New Services**: Update Nacos route configuration to include service endpoints

**Controller Pattern**: Use GoFrame's `g.Meta` tags for API definitions in controller methods

**Service Implementation**: Define interfaces in `/internal/service/` and implement in `/internal/logic/`

**Error Handling**: Use custom error codes from `/internal/consts/errorcodes.go`

**Configuration Access**: Leverage Nacos for dynamic configuration that updates without restart

## File Upload Support
The gateway supports forwarding file uploads to backend microservices while preserving multipart form data structure.