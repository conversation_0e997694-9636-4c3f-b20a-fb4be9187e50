# AI-Gateway 微服務閘道產品維運白皮書

**文件版本：** 2.0
**最後更新：** 2025年6月10日
**目標讀者：** 系統管理員、運維工程師 (O&M)、技術支援團隊

---

## 1. 總覽 (Overview)

### 1.1. 產品定義與目標

**AI-Gateway** 是一個基於 GoFrame 框架開發的高效能、可配置的**微服務 API 閘道**。

其核心目標是作為企業內部微服務架構的**統一對外入口**。它接收所有外部請求，並根據一套動態、可配置的路由規則，將請求安全、準確地轉發至對應的後端內部服務（如 `tenants.svc`, `quizto.svc`, `channelHub.svc` 等）。

透過 AI-Gateway，開發與維運團隊可以實現：

* **集中管理路由**：無需在每個服務中處理複雜的網路路徑。
* **統一處理橫切關注點**：如集中式的身分驗證與授權。
* **簡化客戶端互動**：客戶端只需與閘道這一個端點通訊。

### 1.2. 核心功能摘要

經過對程式碼 (`internal/logic/logic.go`) 與設定檔結構的全面分析，AI-Gateway 的核心功能可歸納如下：

* **動態路由轉發 (Dynamic Routing & Proxy)**：基於設定檔中的 `routes` 規則，將來自特定路徑 (`path`) 的請求，重寫並代理至指定的後端微服務 (`service`) 的目標路徑 (`uri`)。
* **集中式身分驗證 (Centralized Authentication)**：可為每條路由單獨設定是否需要鑑權 (`authorized: true/false`)。若需要，閘道會在轉發請求前，先呼叫統一的認證服務 (`auth.service`) 進行驗證。
* **反向代理核心**：利用 GoFrame 的 HTTP Client 作為反向代理引擎，高效處理請求的接收、修改與轉發。
* **可配置的日誌與伺服器行為**：透過設定檔可以靈活控制日誌級別、分割策略以及伺服器監聽位址等。
* **容器化與雲原生設計**：提供 Dockerfile 和 Kubernetes Kustomize 設定，易於在現代雲端環境中部署和管理。

### 1.3. 目標受眾

本文件主要為負責 **AI-Gateway** 服務部署、設定、監控和日常維運的**技術人員**撰寫。閱讀本文件後，您將能夠：

* 理解系統的完整架構和路由轉發原理。
* 獨立完成服務的部署與設定（特別是路由與鑑權規則）。
* 監控服務運行狀態並進行有效的故障排除。

---

## 2. 系統架構 (System Architecture)

### 2.1. 架構圖

```mermaid
---
config:
  layout: dagre
---
flowchart TD
 subgraph subGraph0["External Clients"]
        A["Web/Mobile App"]
        B["第三方服務"]
  end
 subgraph subGraph1["AI-Gateway Service"]
    direction LR
        D{"路由與鑑權引擎"}
        C["HTTP Server 監聽外部請求"]
        E["鑑權模組"]
        F(("auth.service tenants.svc"))
        G["反向代理模組"]
  end
 subgraph subGraph2["Internal Microservices"]
    direction LR
        H["Tenants Service tenants.svc"]
        I["Quizto Service quizto.svc"]
        J["ChannelHub Service channelHub.svc"]
        K["..."]
  end
 subgraph subGraph3["Configuration Center"]
        N["Nacos / Local File config.yaml"]
  end
    C --> D
    D -- 請求需鑑權 (authorized: true) --> E
    E -- 呼叫認證服務 --> F
    F -- 驗證通過/失敗 --> E
    E -- 驗證通過 --> D
    D -- 根據路由規則 代理請求 --> G
    A --> C
    B --> C
    G -- 轉發至 /v1/tenants/ --> H
    G -- 轉發至 /v1/km/ --> I
    G -- 轉發至 /v1/line/ --> J
    N -- 讀取路由與鑑權設定 --> D
    N -- 讀取認證服務位址 --> E

```

### 2.2. 核心組件說明

* **路由與鑑權引擎 (Routing & Auth Engine)**: 這是閘道的核心中介軟體，對應程式碼為 `internal/logic/logic.go` 的 `MiddlewareHandler`。它在接收到每個請求後，會執行以下邏輯：
    1.  遍歷設定檔中的 `routes` 列表。
    2.  將請求的 URL 路徑與每條規則的 `path` 進行匹配。
    3.  找到匹配的規則後，檢查其 `authorized` 標記。
    4.  如果 `authorized`為 `true`，則呼叫**鑑權模組**。
    5.  鑑權通過後，或如果規則無需鑑權，則將請求交給**反向代理模組**。
* **鑑權模組 (Authentication Module)**: 負責與設定檔中 `auth` 區塊定義的認證服務進行通訊。它會將原始請求的必要資訊（如 Headers）發送到認證服務的 `uri` 進行驗證，並根據回應決定是否放行。
* **反向代理模組 (Reverse Proxy Module)**: 使用 GoFrame 內建的 HTTP Client 實作。它根據匹配到的路由規則，將原始請求的 URL 從 `path` 重寫為 `uri`，然後將請求（包括 Body 和 Headers）發送到目標 `service`。

---

## 3. 核心功能詳解

### 3.1. 動態路徑路由

這是閘道最核心的功能，完全由設定檔中的 `routes` 區塊驅動。

* **運作機制**: 閘道啟動時會讀取並快取路由規則。`MiddlewareHandler` 會對每一個進入的請求，用其 URL Path 依序與 `routes` 列表中的 `path` 進行前綴匹配。
* **路徑重寫**: 一旦匹配成功，閘道會進行路徑重寫。例如，根據您提供的設定：
    * 一個對 `/tenants/users/123` 的請求，會匹配到 `path: /tenants/*`。
    * 閘道會將其轉發到 `tenants.svc` 服務，並且目標路徑會被重寫為 `/v1/tenants/users/123`（即 `uri` + `*` 匹配到的部分）。

**`routes` 規則詳解:**

* `id`: 路由的唯一識別碼，主要用於日誌和追蹤。
* `service`: 目標後端微服務的名稱或位址。
* `path`: 對外暴露的、用於匹配傳入請求的路徑模式。
* `uri`: 重寫後，用於請求後端微服務的內部路徑。
* `authorized`: 布林值，決定此路由是否需要經過集中式鑑權。

### 3.2. 集中式鑑權

此功能為需要保護的微服務提供了一層統一的安全屏障。

* **運作流程**:
    1.  當一個請求匹配到一條 `authorized: true` 的路由規則時，請求會被暫時掛起。
    2.  閘道會向 `auth.service`（如 `tenants.svc`）的 `auth.uri`（如 `/v1/tenants/auth`）發起一個鑑權請求。
    3.  認證服務會執行實際的 Token 驗證、權限檢查等邏輯。
    4.  如果認證服務回傳成功的狀態碼（如 HTTP 200），閘道則繼續執行第 3.1 節的代理轉發流程。
    5.  如果認證服務回傳失敗的狀態碼（如 HTTP 401/403），閘道會立即中斷流程，並將該失敗狀態碼直接回傳給原始客戶端。

---

## 4. 部署與設定指南

### 4.1. 編譯與建置

編譯與建置 Docker 映像檔的流程與先前分析一致，可參考 `build.sh` 與 `manifest/docker/docker.sh` 腳本。

### 4.2. 設定檔詳解 (`config.yaml`)

這是運維工作的核心。**請注意，此結構基於您提供的 Nacos 設定，可能與程式碼庫中的 `hack/config.yaml` 樣例不同。部署時應以此結構為準。**

```yaml
# HTTP 伺服器設定
server:
  address: ":8000"                      # 容器內監聽的埠號
  clientMaxBodySize: "8m"               # 請求體大小限制
  swaggerPath: "/swagger"               # Swagger UI 介面路徑 (若啟用)
  openapiPath: "/api.json"              # OpenAPI 規格路徑 (若啟用)

# 日誌設定
logger:
  path: "/app/logs"                     # 容器內的日誌路徑
  file: "gateway_{Y-m-d}.log"           # 日誌檔案名稱格式
  level: "dev"                          # 日誌級別 (all, dev, prod, info, warn, error)
  stdout: true                          # 是否同時輸出到標準輸出
  rotateSize: "10M"                     # 日誌分割大小
  rotateBackupLimit: 3                  # 保留的備份檔案數量
  rotateBackupExpire: "3d"              # 備份檔案過期時間
  rotateBackupCompress: 9               # 備份檔案壓縮級別

# 集中式鑑權服務設定
auth:
  service: "tenants.svc"                # 認證服務的位址
  uri: "/v1/tenants/auth"               # 認證服務的 API 路徑

# 路由規則列表
routes:
  - id: "tenants"                       # 租戶服務路由
    service: "tenants.svc"
    path: "/tenants/*"
    uri: "/v1/tenants/"
    authorized: false                   # 此路由無需鑑權

  - id: "quizto.svc"                    # 知識庫服務路由
    service: "quizto.svc"
    path: "/km/*"
    uri: "/v1/km/"
    authorized: true                    # 此路由需要鑑權

  - id: "line"                          # Line 相關服務路由
    service: "channelHub.svc"
    path: "/line/*"
    uri: "/v1/line/"
    authorized: true                    # (假設) 需要鑑權

  - id: "attachments"                   # 附件服務路由
    service: "channelHub.svc"
    path: "/attachments/*"
    uri: "/v1/attchments/"              # 注意: 根據截圖，這裡可能有拼寫錯誤 (attchments)
    authorized: true                    # (假設) 需要鑑權
```

### 4.3. Kubernetes 部署

部署流程與先前分析一致，但核心是確保掛載到容器的 `config.yaml` 內容符合 4.2 節的**微服務閘道格式**。

1.  **修改 ConfigMap**:
    編輯 `manifest/deploy/kustomize/overlays/develop/configmap.yaml`，確保其 `data` 欄位下的 `config.yaml` 內容是根據 4.2 節的格式撰寫的，而不是舊的 AI 模型格式。
2.  **修改映像檔與應用部署**:
    這部分流程不變，修改 `deployment.yaml` 中的映像檔標籤，然後執行 `kubectl apply -k`。

---

## 5. 維運與監控

### 5.1. 日誌系統與健康檢查

日誌與健康檢查的機制 (`/healthz` 端點) 基於 GoFrame 框架，與先前分析一致，仍然有效。運維人員應重點關注日誌中關於路由匹配和鑑權的資訊。

### 5.2. 常見問題與故障排除

| 問題現象 | 可能原因 | 排除步驟 |
| :--- | :--- | :--- |
| **請求回傳 404 Not Found** | 1. 請求的 URL 無法匹配 `routes` 中的任何一條 `path` 規則。<br>2. 匹配成功，但後端微服務回傳 404。 | 1. 檢查閘道日誌，確認是否有 "route not found" 或類似錯誤。<br>2. 核對請求 URL 與 `config.yaml` 中的 `routes` 規則，注意 `*` 萬用字元。<br>3. 若日誌顯示已轉發，則需排查對應的後端微服務。 |
| **請求回傳 401 Unauthorized / 403 Forbidden** | 1. 請求的路由 `authorized` 為 `true`，但客戶端未提供有效的認證資訊 (如 Token)。<br>2. 認證服務 (`auth.service`) 回傳了鑑權失敗。 | 1. 確認該路由是否需要鑑權。<br>2. 查看閘道日誌，確認是否發起了對 `auth.service` 的呼叫。<br>3. 檢查 `auth.service` 的日誌，分析鑑權失敗的具體原因。 |
| **請求回傳 5xx 伺服器錯誤** | 1. 閘道無法連接到後端微服務（網路問題、服務未啟動）。<br>2. 閘道無法連接到認證服務。<br>3. 後端微服務本身發生了內部錯誤。 | 1. 查看閘道日誌，定位是連接哪個服務 (`service` 或 `auth.service`) 時出錯。<br>2. 在閘道 Pod 內部嘗試 `curl` 或 `ping` 目標服務，檢查網路連線。<br>3. 如果是後端服務錯誤，需查看對應服務的日誌。 |
| **設定檔更新後路由未生效** | 1. 服務未被設定為動態載入設定。<br>2. K8s ConfigMap 未成功更新到 Pod 中。 | 1. 當前程式碼似乎是在啟動時載入設定。更新設定後需要**重啟 Pod** 才能生效。<br>2. 執行 `kubectl describe pod <pod-name>` 確認掛載的 ConfigMap 版本是否為最新。 |

