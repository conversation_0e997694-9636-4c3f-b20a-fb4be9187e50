# AI-Gateway 微服務閘道技術設計規格文件

**文件版本：** 1.0
**目標讀者：** 後端開發工程師、系統架構師、技術主管

---

## 1. 介紹 (Introduction)

### 1.1. 專案目標與範圍
本文件旨在詳細闡述 **AI-Gateway** 微服務閘道的內部設計與實作細節。此閘道的核心設計目標是提供一個可配置、高效能的單一入口點，用於路由與保護後端的微服務叢集。

**專案範圍 (In-Scope):**
* 實作一個基於設定檔的動態路由反向代理。
* 實作一個集中式的、可選的請求鑑權中介軟體。
* 提供清晰的資料結構以進行設定。
* 保持系統無狀態，以利水平擴展。

**非專案目標 (Out-of-Scope):**
* 實作具體的鑑權邏輯（鑑權被委派給外部服務）。
* 服務發現（後端服務位址需在設定中明確指定）。
* 請求的負載平衡（由底層設施如 Kubernetes Service 或服務網格處理）。
* 動態設定熱更新（目前設計為啟動時載入）。

### 1.2. 核心術語
* **Gateway (閘道)**: 指的是本專案，作為所有流量的入口。
* **Route (路由)**: 一條定義了「如何將一個外部路徑映射到一個內部服務」的規則。
* **Upstream Service (上游服務)**: 指閘道後方的後端微服務，是請求的最終處理者。
* **Auth Service (認證服務)**: 一個獨立的、用於驗證請求合法性的微服務。

---

## 2. 系統設計 (System Design)

### 2.1. 高階架構
AI-Gateway 的設計遵循標準的**反向代理**與**API 閘道**模式。它在應用層（L7）運作，部署在客戶端與上游服務之間。其核心是一個全域 HTTP 中介軟體 (`MiddlewareHandler`)，它攔截所有傳入的請求，並在將其代理到上游服務之前應用路由與鑑權邏輯。

系統設計的核心思想是 **「設定即邏輯」**。所有的路由和鑑權行為都由外部設定檔 (`config.yaml`) 驅動，使得閘道本身的核心程式碼保持通用和穩定。

### 2.2. 資料模型 (Data Models)
系統的行為由一個核心的 `Config` 結構體定義，該結構體在 `internal/model/svc.go` 中宣告。閘道在啟動時，會將 `config.yaml` 的內容解析並載入到此結構的實例中。

#### 2.2.1. `Config` 結構
```go
// internal/model/svc.go
type Config struct {
	Auth   Auth    `json:"auth"`
	Routes []Route `json:"routes"`
}
```
* **`Auth`**: 定義了集中式鑑權服務的相關設定。
* **`Routes`**: 一個 `Route` 結構的陣列，定義了所有可用的路由規則。

#### 2.2.2. `Auth` 結構
```go
// internal/model/svc.go
type Auth struct {
	Service string `json:"service"`
	Uri     string `json:"uri"`
}
```
* **`Service`**: 認證服務的位址 (例如 `tenants.svc`)。
* **`Uri`**: 認證服務的 API 路徑 (例如 `/v1/tenants/auth`)。閘道會將鑑權請求發送到 `Service + Uri`。

#### 2.2.3. `Route` 結構
```go
// internal/model/svc.go
type Route struct {
	Id         string `json:"id"`
	Service    string `json:"service"`
	Path       string `json:"path"`
	Uri        string `json:"uri"`
	Authorized bool   `json:"authorized"`
}
```
這是系統中最重要的資料結構，每一條 `Route` 都定義了一個完整的轉發規則。
* **`Id`**: 路由的字串識別碼，主要用於日誌記錄和問題追蹤。
* **`Service`**: 此路由對應的上游服務位址。
* **`Path`**: 對外暴露的、用於匹配傳入請求 URL 的**前綴路徑**。結尾的 `*` 是一個設計約定，表示萬用字元匹配。
* **`Uri`**: 用於重寫請求 URL 的內部目標路徑**前綴**。
* **`Authorized`**: 一個布林標記，`true` 表示所有匹配此規則的請求都必須先通過 `Auth Service` 的驗證。

---

## 3. 核心邏輯與演算法 (Core Logic & Algorithms)

系統的核心商業邏輯完全包含在 `internal/logic/logic.go` 的 `MiddlewareHandler` 函式中。這是一個 GoFrame 中介軟體，會在每個請求到達時被執行。

### 3.1. 請求處理流程 (Sequence Diagram)

```mermaid
sequenceDiagram
    participant Client as 客戶端
    participant Gateway as AI-Gateway (MiddlewareHandler)
    participant AuthService as 認證服務
    participant UpstreamService as 上游服務

    Client->>Gateway: 發起請求 (e.g., GET /km/articles/1)
    activate Gateway
    
    Gateway->>Gateway: 遍歷 `config.Routes` 列表
    Note right of Gateway: 尋找匹配 `path` 的規則
    
    Gateway->>Gateway: 找到匹配規則 (id: quizto.svc, path: /km/*, authorized: true)
    
    alt 規則需鑑權 (authorized == true)
        Gateway->>AuthService: 呼叫 `auth.service` 的 `auth.uri`
        activate AuthService
        AuthService-->>Gateway: 回傳鑑權結果 (e.g., HTTP 200 OK)
        deactivate AuthService

        alt 鑑權失敗
            Gateway-->>Client: 回傳鑑權失敗狀態碼 (e.g., 401/403)
        else 鑑權通過
            Note right of Gateway: 鑑權通過，繼續執行
            Gateway->>Gateway: 執行路徑重寫與代理 (Proxy)
            Gateway->>UpstreamService: 發送重寫後的請求 (e.g., GET /v1/km/articles/1)
            activate UpstreamService
            
            UpstreamService-->>Gateway: 回傳回應
            deactivate UpstreamService

            Gateway-->>Client: 將上游服務的回應轉發給客戶端
        end
    else 規則無需鑑權
        Note right of Gateway: 鑑權無需，繼續執行
        Gateway->>Gateway: 執行路徑重寫與代理 (Proxy)
        Gateway->>UpstreamService: 發送重寫後的請求 (e.g., GET /v1/km/articles/1)
        activate UpstreamService
        
        UpstreamService-->>Gateway: 回傳回應
        deactivate UpstreamService

        Gateway-->>Client: 將上游服務的回應轉發給客戶端
    end
    deactivate Gateway

```

### 3.2. 路由匹配演算法
* **觸發**: `MiddlewareHandler` 接收到一個 `ghttp.Request` 物件。
* **實作**:
    1.  從 `g.Cfg()` 取得已載入的 `Config` 物件。
    2.  初始化一個 `isMatch` 布林變數為 `false`。
    3.  **迴圈**：遍歷 `Config.Routes` 陣列中的每一條 `route`。
    4.  **路徑匹配**:
        * 將 `route.Path` 中的 `*` 移除，得到基礎匹配路徑 (e.g., `/tenants/`)。
        * 使用 `gstr.HasPrefix(r.URL.Path, basePath)` 檢查請求的路徑是否以該基礎路徑開頭。
    5.  **找到匹配**:
        * 若匹配成功，將 `isMatch` 設為 `true`。
        * 執行**鑑權檢查**（詳見 3.3）。
        * 若鑑權通過或無需鑑權，執行**反向代理**（詳見 3.4）。
        * **中斷迴圈**：使用 `break` 終止遍歷，因為只處理第一條匹配的規則。
* **未找到匹配**: 如果迴圈結束後 `isMatch` 仍為 `false`，閘道會回傳 HTTP 404 Not Found。

### 3.3. 鑑權邏輯
* **觸發**: 在路由匹配成功後，檢查 `route.Authorized` 欄位。
* **實作**:
    1.  若 `route.Authorized` 為 `true`：
    2.  建構一個新的 HTTP 請求，目標為 `config.Auth.Service + config.Auth.Uri`。
    3.  **複製 Headers**: 將原始請求的所有 Headers 複製到這個新的鑑權請求中，以傳遞 Token 等認證資訊。
    4.  使用 `g.Client()` 發送鑑權請求。
    5.  **結果判斷**: 檢查鑑權服務的回應。如果狀態碼不是 `ghttp.StatusOK` (200)，則閘道會立即將此錯誤狀態碼回傳給客戶端，並中斷請求流程。

### 3.4. 反向代理實作
* **觸發**: 鑑權流程完成後（或無需鑑權時）。
* **實作**: 呼叫 `Proxy(r, route)` 函式。
    1.  **路徑重寫**:
        * 將 `route.Path` 中的 `*` 移除，得到基礎路徑 `basePath`。
        * 使用 `gstr.Split(r.URL.Path, basePath)[1]` 取得原始請求路徑中在 `basePath` 之後的部分 (e.g., `users/123`)。
        * 建構新的目標 URL：`route.Service + route.Uri + <sub-path>`。
    2.  **建立代理請求**:
        * 使用 `g.Client().New()` 建立一個 HTTP Client 實例。
        * 設定代理請求的方法、URL、Body 和 Headers，完全複製自原始請求 `r`。
    3.  **執行請求**: 呼叫 `DoRequest` 將請求發送到上游服務。
    4.  **回應處理**:
        * 將上游服務回傳的狀態碼、Headers 和 Body 完整地寫入到原始請求的 `r.Response` 中。這一步完成了將後端回應透傳給客戶端的過程。

---

## 4. 設定與環境 (Configuration & Environment)

### 4.1. 設定管理
* **核心機制**: 系統強依賴 GoFrame 的內建設定管理 (`g.Cfg()`)。
* **載入時機**: 設定在服務啟動時由框架自動載入。
* **設定源**: GoFrame 支援多種設定源，預設是專案根目錄下的 `config.yaml`。在生產環境中，可以透過環境變數或啟動參數將其指向 Nacos、Etcd 等設定中心。
* **熱更新**: 當前的實作 (`internal/logic/logic.go` 中直接從 `g.Cfg()` 讀取) **不支援**設定的熱更新。每次從 `g.Cfg()` 讀取時，如果框架支援，可能會讀到最新的，但由於 `Config` 物件在每次請求處理時都會被完整讀取，這可能會有效能影響。若要支援高效的熱更新，應考慮使用 `gcfg.Instance().Scan` 結合監聽器模式，在設定變更時更新一個全域的、線程安全的 `Config` 變數。

### 4.2. Kubernetes 資源設計
`manifest/` 目錄下的 Kubernetes 設定體現了以下設計決策：
* **ConfigMap**: `config.yaml` 被抽象為 `ConfigMap`，實現了設定與映像檔的分離。
* **Deployment**:
    * 採用無狀態設計，可以安全地設定多個 `replicas` 進行擴展。
    * 定義了 `livenessProbe` 和 `readinessProbe`，指向 `/healthz` 端點。這個端點由 GoFrame 預設提供，只要 HTTP 伺服器能正常回應即返回 200，是基礎的存活探測。
* **Service**: 建立一個 `ClusterIP` 類型的服務，為閘道提供一個穩定的內部網路端點，供 Ingress Controller 或其他內部服務呼叫。

---

## 5. 錯誤處理 (Error Handling)

* **路由未找到**: `MiddlewareHandler` 中有明確的邏輯，在遍歷完所有規則後若未匹配，則回傳 404。
* **鑑權失敗**: 直接將 `Auth Service` 回傳的非 200 狀態碼透傳給客戶端。
* **代理失敗**:
    * 若因網路問題（如DNS解析失敗、連線逾時）無法連接到上游服務，GoFrame 的 HTTP Client 會回傳錯誤。此時閘道會在日誌中記錄詳細錯誤，並可能向客戶端回傳 502 Bad Gateway 或 504 Gateway Timeout。
    * 若上游服務回傳錯誤（如 4xx 或 5xx），閘道會忠實地將其透傳給客戶端。

---
