server:
  address: "*************:8083,************:8083"
  swaggerPath: "/swagger"
  openapiPath: "/api.json"
logger:
  path: "./logs"
  file:  "gateway_{Y-m-d}.log"
  rotateSize: "10M"
  rotateBackupLimit: 3 
  rotateBackupExpire: "3d"
  rotateBackupCompress: 9
  level: "dev"
  stdout: true
auth:
 service: tenants.svc
 uri: /v1/tenants/auth

routes:
  - id: tenants.svc
    path: /tenants/*
    uri: /v1/tenants/
    authorized: false
  - id: quizto.svc
    path: /km/*
    uri: /v1/km/
    authorized: true
  - id: vecguard.svc
    path: /vecguard/*
    uri: /v1/vec/
    authorized: true