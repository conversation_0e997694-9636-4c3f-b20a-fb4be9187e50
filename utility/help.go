package utility

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// ParseRes 组合新的response 消息返回
func ParseRes(err error, data ...g.Var) (res *gjson.Json) {
	res = gjson.New("")
	_ = res.Set("code", gerror.Code(err).Code())
	if !g.Is<PERSON>mpty(gerror.Code(err).Message()) {
		_ = res.Set("message", gerror.Code(err).Message())
	} else {
		_ = res.Set("message", err.Error())
	}

	if len(data) > 0 {
		_ = res.Set("data", data[0])
	}
	return
}
