# AI Gateway Test Suite

This test suite provides comprehensive testing for the AI Gateway service built with GoFrame v2.

## Test Structure

### 1. Unit Tests

#### Service Logic Tests (`internal/logic/svc/svc_test.go`)
- **ReplaceToInstanceUri**: Tests URI parsing and service resolution
  - Valid URI handling
  - Invalid URI detection
  - Service not found scenarios
  - HTTPS scheme support
  - Default scheme handling

- **ValidateTenant**: Tests tenant authentication
  - Empty tenant ID validation
  - Empty app key validation
  - Integration tests require mock HTTP client

#### Controller Tests (`internal/controller/svc/svc_v1_svc_test.go`)
- **Gateway Routing**: Tests request forwarding logic
  - Valid requests with authorization
  - Public service requests without auth
  - Invalid service handling
  - Unauthorized request handling
  - Service name with .svc suffix

- **File Upload**: Tests multipart form handling
  - Single file upload
  - Multiple file upload
  - Form data preservation

### 2. Integration Tests (`internal/integration_test.go`)

- **Full Request Flow**: End-to-end testing
  - Request routing through gateway
  - Backend service communication
  - Authentication flow
  - Response forwarding

- **Header Forwarding**: Ensures headers are preserved
  - Tenant ID forwarding
  - App key forwarding
  - Auth token forwarding

### 3. Mock Tests (`internal/service/nacos_test.go`)

- **Nacos Configuration**: Tests config management
  - Configuration loading
  - Hot reload functionality
  - Service registration
  - Route parsing
  - Environment-specific configs

### 4. Test Utilities (`internal/testutil/testutil.go`)

Provides helper functions for testing:
- `CreateTestRequest`: Creates HTTP test requests
- `CreateAuthenticatedRequest`: Creates authenticated requests
- `MockServiceRoutes`: Creates mock service configurations
- `MockAuthServer`: Creates mock authentication server
- `MockBackendServer`: Creates mock backend services
- `AssertJSONResponse`: JSON response validation

## Running Tests

### Run all tests
```bash
go test ./...
```

### Run tests with coverage
```bash
go test -cover ./...
```

### Run specific test package
```bash
go test ./internal/logic/svc
go test ./internal/controller/svc
```

### Run integration tests
```bash
go test ./internal -run TestIntegration
```

### Run with verbose output
```bash
go test -v ./...
```

## Test Coverage Areas

1. **Service Discovery**
   - Route configuration parsing
   - Service URL construction
   - Authorization flag handling

2. **Request Processing**
   - URI parsing and validation
   - Request forwarding
   - Response handling
   - Error scenarios

3. **Authentication**
   - Tenant validation
   - Header forwarding
   - Auth service integration

4. **File Handling**
   - Multipart form processing
   - File upload forwarding
   - Temporary file management

5. **Configuration**
   - Nacos integration
   - Dynamic config updates
   - Environment-specific settings

## Mock Services

The test suite includes several mock services:

1. **Mock Backend Server**: Simulates microservices
   - `/api/v1/users/list`: Returns user list
   - `/api/v1/data/create`: Echoes request data
   - `/auth/validate`: Validates tenant credentials

2. **Mock Nacos Client**: Simulates configuration service
   - Config storage and retrieval
   - Change listeners
   - Hot reload simulation

## Best Practices

1. **Test Isolation**: Each test is independent
2. **Mock External Dependencies**: No real network calls
3. **Comprehensive Coverage**: Test both success and failure paths
4. **Clear Test Names**: Descriptive test function names
5. **Test Data**: Use consistent test data patterns

## Adding New Tests

When adding new functionality:

1. Create unit tests for business logic
2. Add controller tests for HTTP handling
3. Include integration tests for end-to-end flows
4. Update mock services as needed
5. Document test scenarios in this README