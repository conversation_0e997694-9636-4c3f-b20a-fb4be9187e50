package svc

import (
	"ai-gateway/internal/model"
	"context"
	"testing"

	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/test/gtest"
)

// TestCheckOrCreateTenant 測試CheckOrCreateTenant方法
func TestCheckOrCreateTenant(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 測試空租戶ID的情況
		svc := &sSVC{
			client: g.Client(),
		}

		err := svc.CheckOrCreateTenant(context.Background(), "")
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "Tenant ID is empty")
	})

	gtest.C(t, func(t *gtest.T) {
		// 測試基本功能（不依賴外部配置）
		// 這個測試主要驗證方法的基本邏輯結構
		svc := &sSVC{
			client: g.Client(),
		}

		// 由於沒有配置，應該返回服務不存在的錯誤
		err := svc.CheckOrCreateTenant(context.Background(), "test-tenant")
		t.AssertNE(err, nil)
	})
}

// TestValidateTenant 測試ValidateTenant方法（確保重構後仍然正常工作）
func TestValidateTenant(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 測試空租戶ID
		svc := &sSVC{
			client: g.Client(),
		}

		input := model.ValidateTenantInput{
			TenantID: "",
		}

		passed, err := svc.ValidateTenant(context.Background(), input)
		t.AssertNE(err, nil)
		t.Assert(passed, false)
	})

	gtest.C(t, func(t *gtest.T) {
		// 測試基本功能（不依賴外部配置）
		svc := &sSVC{
			client: g.Client(),
		}

		input := model.ValidateTenantInput{
			TenantID: "test-tenant",
			AppKey:   "test-app-key",
			Svc:      "test-service",
			Token:    "test-token",
			Action:   "test-action",
		}

		// 由於沒有配置，應該返回服務不存在的錯誤
		passed, err := svc.ValidateTenant(context.Background(), input)
		t.AssertNE(err, nil)
		t.Assert(passed, false)
	})
}

// TestReplaceToInstanceUri 測試URI解析功能（確保重構後仍然正常工作）
func TestReplaceToInstanceUri(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 創建測試服務實例
		svc := &sSVC{
			serviceNameToUri: gmap.NewStrAnyMap(),
			client:           g.Client(),
		}

		// 手動添加測試路由
		testRoute := &model.ServicesUri{
			Id:         "test.svc",
			Path:       "/test/*",
			Uri:        "/v1/test/",
			Service:    "test.svc",
			Authorized: true,
			Check:      true,
		}
		svc.serviceNameToUri.Set("test", testRoute)

		// 測試URI解析
		input := model.ParseURIInput{
			RequestURI: "test/action",
		}

		output, err := svc.ReplaceToInstanceUri(context.Background(), input)
		t.AssertNil(err)
		t.AssertNE(output, nil)
		t.Assert(output.URI, "/v1/test/action")
		t.Assert(output.Authorized, true)
		t.Assert(output.Check, true)
	})

	gtest.C(t, func(t *gtest.T) {
		// 測試服務不存在的情況
		svc := &sSVC{
			serviceNameToUri: gmap.NewStrAnyMap(),
			client:           g.Client(),
		}

		input := model.ParseURIInput{
			RequestURI: "nonexistent/action",
		}

		output, err := svc.ReplaceToInstanceUri(context.Background(), input)
		t.AssertNE(err, nil)
		t.AssertNil(output)
	})
}
