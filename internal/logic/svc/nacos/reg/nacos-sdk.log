2025-08-07T18:34:20.131+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-08-07T18:34:20.132+0800	ERROR	security/security_proxy.go:117	login in err:Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connect: connection refused
2025-08-07T18:34:20.133+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55186
2025-08-07T18:34:20.133+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=c01f2e03-a407-4591-9b23-93a73fb3a87c)
2025-08-07T18:34:20.133+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-08-07T18:34:20.133+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-08-07T18:34:20.133+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-08-07T18:34:20.133+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] c01f2e03-a407-4591-9b23-93a73fb3a87c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-08-07T18:34:20.133+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-07T18:34:20.133+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-07T18:34:20.133+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-07T18:34:20.133+0800	INFO	util/common.go:96	Local IP:************
2025-08-07T18:34:20.134+0800	WARN	rpc/rpc_client.go:329	[RpcClient.Start] c01f2e03-a407-4591-9b23-93a73fb3a87c fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused", start up retry times left=2
2025-08-07T18:34:20.134+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] c01f2e03-a407-4591-9b23-93a73fb3a87c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-08-07T18:34:20.134+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-07T18:34:20.134+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-07T18:34:20.134+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-07T18:34:20.135+0800	WARN	rpc/rpc_client.go:329	[RpcClient.Start] c01f2e03-a407-4591-9b23-93a73fb3a87c fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused", start up retry times left=1
2025-08-07T18:34:20.135+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] c01f2e03-a407-4591-9b23-93a73fb3a87c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-08-07T18:34:20.135+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-07T18:34:20.135+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-07T18:34:20.135+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-07T18:34:20.135+0800	WARN	rpc/rpc_client.go:329	[RpcClient.Start] c01f2e03-a407-4591-9b23-93a73fb3a87c fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused", start up retry times left=0
2025-08-07T18:34:20.135+0800	INFO	rpc/rpc_client.go:426	c01f2e03-a407-4591-9b23-93a73fb3a87c try to re connect to a new server, server is not appointed, will choose a random server.
2025-08-07T18:34:20.135+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-07T18:34:20.135+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-07T18:34:20.135+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
