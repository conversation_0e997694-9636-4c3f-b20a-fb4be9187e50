2025-08-07T18:34:20.135+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-07T18:34:20.135+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-07T18:34:20.135+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-07T18:34:20.135+0800	ERROR	security/security_proxy.go:117	login in err:Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connect: connection refused
2025-08-07T18:34:20.135+0800	WARN	config_client/config_client.go:335	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/ai-gateway.yaml@@DEFAULT_G<PERSON>UP@@dev.: file not exist
2025-08-07T18:34:20.136+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/ai-gateway.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-08-07T18:34:20.136+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/ai-gateway.yaml@@DEFAULT_GROUP@@dev_failover.
2025-08-07T18:34:20.136+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-dd24a971-9ccb-43c2-be40-53c09869fc1e)
2025-08-07T18:34:20.136+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-08-07T18:34:20.136+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-08-07T18:34:20.136+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-08-07T18:34:20.136+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-dd24a971-9ccb-43c2-be40-53c09869fc1e try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-08-07T18:34:20.136+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-07T18:34:20.136+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-07T18:34:20.136+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-07T18:34:20.136+0800	WARN	rpc/rpc_client.go:457	c01f2e03-a407-4591-9b23-93a73fb3a87c fail to connect server, after trying 1 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused"
2025-08-07T18:34:20.136+0800	WARN	rpc/rpc_client.go:329	[RpcClient.Start] config-0-dd24a971-9ccb-43c2-be40-53c09869fc1e fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused", start up retry times left=2
2025-08-07T18:34:20.136+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-dd24a971-9ccb-43c2-be40-53c09869fc1e try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-08-07T18:34:20.136+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-07T18:34:20.136+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-07T18:34:20.136+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-07T18:34:20.136+0800	WARN	rpc/rpc_client.go:329	[RpcClient.Start] config-0-dd24a971-9ccb-43c2-be40-53c09869fc1e fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused", start up retry times left=1
2025-08-07T18:34:20.136+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-dd24a971-9ccb-43c2-be40-53c09869fc1e try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-08-07T18:34:20.136+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-07T18:34:20.136+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-07T18:34:20.136+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-07T18:34:20.136+0800	WARN	rpc/rpc_client.go:329	[RpcClient.Start] config-0-dd24a971-9ccb-43c2-be40-53c09869fc1e fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused", start up retry times left=0
2025-08-07T18:34:20.136+0800	INFO	rpc/rpc_client.go:426	config-0-dd24a971-9ccb-43c2-be40-53c09869fc1e try to re connect to a new server, server is not appointed, will choose a random server.
2025-08-07T18:34:20.136+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-07T18:34:20.136+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-07T18:34:20.136+0800	ERROR	rpc/rpc_client.go:629	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"ai-gateway.yaml","tenant":"dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.3.2/common/remote/rpc/rpc_client.go:591
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.3.2/clients/config_client/config_proxy.go:63
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.3.2/clients/config_client/config_proxy.go:128
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.3.2/clients/config_client/config_client.go:198
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.3.2/clients/config_client/config_client.go:166
github.com/gogf/gf/contrib/config/nacos/v2.(*Client).updateLocalValue
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/config/nacos/v2@v2.9.0/nacos.go:108
github.com/gogf/gf/contrib/config/nacos/v2.(*Client).Get
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/config/nacos/v2@v2.9.0/nacos.go:88
github.com/gogf/gf/v2/os/gcfg.(*Config).Get
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/os/gcfg/gcfg.go:103
ai-gateway/internal/logic/svc.(*sSVC).readRouters
	/Users/<USER>/Source/AI/ai-gateway/internal/logic/svc/svc.go:50
ai-gateway/internal/logic/svc.New
	/Users/<USER>/Source/AI/ai-gateway/internal/logic/svc/svc.go:41
ai-gateway/internal/logic/svc.init.0
	/Users/<USER>/Source/AI/ai-gateway/internal/logic/svc/svc.go:23
runtime.doInit1
	/usr/local/go/src/runtime/proc.go:7290
runtime.doInit
	/usr/local/go/src/runtime/proc.go:7257
runtime.main
	/usr/local/go/src/runtime/proc.go:254
runtime.goexit
	/usr/local/go/src/runtime/asm_arm64.s:1223
2025-08-07T18:34:20.136+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-07T18:34:20.137+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-07T18:34:20.137+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-07T18:34:20.137+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-07T18:34:20.137+0800	WARN	rpc/rpc_client.go:457	config-0-dd24a971-9ccb-43c2-be40-53c09869fc1e fail to connect server, after trying 1 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused"
2025-08-07T18:34:20.237+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-07T18:34:20.237+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-07T18:34:20.237+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-07T18:34:20.237+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-07T18:34:20.237+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-07T18:34:20.237+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-07T18:34:20.237+0800	ERROR	rpc/rpc_client.go:629	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"ai-gateway.yaml","tenant":"dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.3.2/common/remote/rpc/rpc_client.go:591
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.3.2/clients/config_client/config_proxy.go:63
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.3.2/clients/config_client/config_proxy.go:128
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.3.2/clients/config_client/config_client.go:198
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.3.2/clients/config_client/config_client.go:166
github.com/gogf/gf/contrib/config/nacos/v2.(*Client).updateLocalValue
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/config/nacos/v2@v2.9.0/nacos.go:108
github.com/gogf/gf/contrib/config/nacos/v2.(*Client).Get
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/config/nacos/v2@v2.9.0/nacos.go:88
github.com/gogf/gf/v2/os/gcfg.(*Config).Get
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/os/gcfg/gcfg.go:103
ai-gateway/internal/logic/svc.(*sSVC).readRouters
	/Users/<USER>/Source/AI/ai-gateway/internal/logic/svc/svc.go:50
ai-gateway/internal/logic/svc.New
	/Users/<USER>/Source/AI/ai-gateway/internal/logic/svc/svc.go:41
ai-gateway/internal/logic/svc.init.0
	/Users/<USER>/Source/AI/ai-gateway/internal/logic/svc/svc.go:23
runtime.doInit1
	/usr/local/go/src/runtime/proc.go:7290
runtime.doInit
	/usr/local/go/src/runtime/proc.go:7257
runtime.main
	/usr/local/go/src/runtime/proc.go:254
runtime.goexit
	/usr/local/go/src/runtime/asm_arm64.s:1223
2025-08-07T18:34:20.237+0800	WARN	rpc/rpc_client.go:457	c01f2e03-a407-4591-9b23-93a73fb3a87c fail to connect server, after trying 2 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused"
2025-08-07T18:34:20.237+0800	WARN	rpc/rpc_client.go:457	config-0-dd24a971-9ccb-43c2-be40-53c09869fc1e fail to connect server, after trying 2 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused"
2025-08-07T18:34:20.338+0800	ERROR	rpc/rpc_client.go:629	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"ai-gateway.yaml","tenant":"dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.3.2/common/remote/rpc/rpc_client.go:591
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.3.2/clients/config_client/config_proxy.go:63
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.3.2/clients/config_client/config_proxy.go:128
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.3.2/clients/config_client/config_client.go:198
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.3.2/clients/config_client/config_client.go:166
github.com/gogf/gf/contrib/config/nacos/v2.(*Client).updateLocalValue
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/config/nacos/v2@v2.9.0/nacos.go:108
github.com/gogf/gf/contrib/config/nacos/v2.(*Client).Get
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/config/nacos/v2@v2.9.0/nacos.go:88
github.com/gogf/gf/v2/os/gcfg.(*Config).Get
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/os/gcfg/gcfg.go:103
ai-gateway/internal/logic/svc.(*sSVC).readRouters
	/Users/<USER>/Source/AI/ai-gateway/internal/logic/svc/svc.go:50
ai-gateway/internal/logic/svc.New
	/Users/<USER>/Source/AI/ai-gateway/internal/logic/svc/svc.go:41
ai-gateway/internal/logic/svc.init.0
	/Users/<USER>/Source/AI/ai-gateway/internal/logic/svc/svc.go:23
runtime.doInit1
	/usr/local/go/src/runtime/proc.go:7290
runtime.doInit
	/usr/local/go/src/runtime/proc.go:7257
runtime.main
	/usr/local/go/src/runtime/proc.go:254
runtime.goexit
	/usr/local/go/src/runtime/asm_arm64.s:1223
2025-08-07T18:34:20.439+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-07T18:34:20.439+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-07T18:34:20.439+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-07T18:34:20.439+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-07T18:34:20.439+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-07T18:34:20.439+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-07T18:34:20.439+0800	ERROR	config_client/config_client.go:201	get config from server error:client not connected, current status:STARTING, dataId=ai-gateway.yaml, group=DEFAULT_GROUP, namespaceId=dev
