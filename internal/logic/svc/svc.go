package svc

import (
	"ai-gateway/boot"
	"ai-gateway/internal/consts"
	"ai-gateway/internal/model"
	"ai-gateway/internal/service"
	"context"
	"fmt"
	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/net/gsvc"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"net/http"
)

func init() {
	service.RegisterSVC(New())
}

type sSVC struct {
	serviceNameToUri *gmap.StrAnyMap
	client           *gclient.Client
}

func New() service.ISVC {
	boot.WaitReady()
	ctx := gctx.GetInitCtx()
	s := &sSVC{
		serviceNameToUri: gmap.NewStrAnyMap(),
		client:           g.Client(),
	}

	s.client.SetDiscovery(gsvc.GetRegistry())

	if e := s.readRouters(ctx); e != nil {
		panic(e)
	}

	return s
}

func (s *sSVC) readRouters(ctx context.Context) (err error) {
	var routes *g.Var
	routes, err = g.Cfg().Get(ctx, "routes")
	if err != nil {
		return err
	}

	for _, m := range routes.Array() {
		var d *model.ServicesUri
		_ = gconv.Scan(m, &d)
		if d != nil {
			s.serviceNameToUri.Set(d.Id, d)
		}
	}

	return
}
func (s *sSVC) logger() *glog.Logger {
	return g.Log().Cat(consts.CatSVC)
}
func (s *sSVC) ReplaceToInstanceUri(ctx context.Context, in model.ParseURIInput) (out *model.ParseURIOutput, err error) {
	out = &model.ParseURIOutput{}

	reqUri := gstr.SplitAndTrim(in.RequestURI, `/`)
	if len(reqUri) < 3 {

		err = gerror.NewCode(consts.BadRequestUri)
		return
	}
	if !s.serviceNameToUri.Contains(reqUri[0]) {
		err = gerror.NewCode(consts.ServiceIsNotExist)
		return
	} else {
		var d *model.ServicesUri
		_ = gconv.Scan(s.serviceNameToUri.Get(reqUri[0]), &d)
		if d != nil {
			err, out.URI = d.ParseUri(fmt.Sprintf("/%s/*", reqUri[1]), gstr.Join(reqUri[2:], `/`))
			if err != nil {
				return
			}

			out.Authorized = d.Authorized
			out.Check = d.Check
			schema := "http"
			if !g.IsEmpty(d.Scheme) {
				schema = d.Scheme
			}

			out.URL = fmt.Sprintf("%s://%s%s", schema, d.Service, out.URI)

		}

	}

	return

}

func (s *sSVC) ValidateTenant(ctx context.Context, in model.ValidateTenantInput) (passed bool, err error) {

	s.logger().Infof(ctx, "Validate  tenant %s with token ", in.TenantID)
	if g.IsEmpty(in.TenantID) {

		err = gerror.NewCode(consts.GeneralError, "Tenant or app key is empty")
		return
	}

	vAuthServiceName, _ := g.Cfg().Get(ctx, "tenants.service", "")
	vAuthUri, _ := g.Cfg().Get(ctx, "tenants.auth_uri", "")
	vScheme, _ := g.Cfg().Get(ctx, "tenants.scheme", "http")
	if vAuthServiceName.IsEmpty() {
		err = gerror.NewCode(consts.ServiceIsNotExist)
		return
	}

	scheme := vScheme.String()
	url := ""

	url = fmt.Sprintf("%s://%s%s", scheme, vAuthServiceName.String(), vAuthUri.String())
	g.Log().Cat(consts.DEBUG).Debugf(ctx, "Auth with tenant id:%s ,to service url [%s]", in.TenantID, url)
	resp, err := s.client.SetHeaderMap(g.MapStrStr{
		consts.HeaderTenantID: in.TenantID,
		consts.HeaderAppKey:   in.AppKey,
		consts.HeaderAuth:     in.Token,
	}).Post(ctx, url, g.Map{"service": in.Svc, "action": in.Action})
	if err != nil {
		g.Log(consts.ERROR).Error(ctx, err)
		return false, err

	}
	defer func() { _ = resp.Close() }()
	g.Log().Cat(consts.DEBUG).Debugf(ctx, `Auth tenant id : %s ,service return result: %v`,
		in.TenantID,
		resp.StatusCode,
	)
	passed = resp.StatusCode == http.StatusOK

	return passed, nil

}

// CheckOrCreateTenant 檢查或創建租戶
func (s *sSVC) CheckOrCreateTenant(ctx context.Context, tenantID string) error {
	s.logger().Infof(ctx, "Check or create tenant %s", tenantID)

	if g.IsEmpty(tenantID) {
		err := gerror.NewCode(consts.GeneralError, "Tenant ID is empty")
		s.logger().Errorf(ctx, "Failed to check or create tenant: tenant ID is empty")
		return err
	}

	// 獲取配置
	vServiceName, _ := g.Cfg().Get(ctx, "tenants.service", "")
	vCheckUri, _ := g.Cfg().Get(ctx, "tenants.check_uri", "")
	vScheme, _ := g.Cfg().Get(ctx, "tenants.scheme", "http")

	if vServiceName.IsEmpty() {
		err := gerror.NewCode(consts.ServiceIsNotExist, "Tenants service not configured")
		s.logger().Errorf(ctx, "Failed to check or create tenant %s: service not configured", tenantID)
		return err
	}

	// 構建請求URL
	scheme := vScheme.String()
	url := fmt.Sprintf("%s://%s%s", scheme, vServiceName.String(), vCheckUri.String())

	s.logger().Debugf(ctx, "Check or create tenant %s, request URL: %s", tenantID, url)

	// 發送請求
	requestBody := g.Map{"tenant_id": tenantID}
	resp, err := s.client.Post(ctx, url, requestBody)
	if err != nil {
		s.logger().Errorf(ctx, "Failed to check or create tenant %s: %v", tenantID, err)
		return err
	}
	defer func() { _ = resp.Close() }()

	s.logger().Debugf(ctx, "Check or create tenant %s, response status: %d", tenantID, resp.StatusCode)

	// 根據響應狀態記錄日誌
	if resp.StatusCode == http.StatusOK {
		s.logger().Infof(ctx, "Successfully checked or created tenant %s", tenantID)
	} else {
		s.logger().Errorf(ctx, "Failed to check or create tenant %s, status code: %d", tenantID, resp.StatusCode)
		return gerror.NewCodef(consts.GeneralError, "Failed to check or create tenant, status: %d", resp.StatusCode)
	}

	return nil
}
