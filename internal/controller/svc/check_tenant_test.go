package svc

import (
	"ai-gateway/internal/service"
	"context"
	"fmt"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
	"github.com/gogf/gf/v2/text/gstr"
)

// TestCheckOrCreateTenantServiceIntegration 測試CheckOrCreateTenant服務方法的集成
func TestCheckOrCreateTenantServiceIntegration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 測試CheckOrCreateTenant服務方法被正確調用
		checkOrCreateCalled := false
		var capturedTenantID string

		mockSVC := &MockSVC{
			CheckOrCreateTenantFn: func(ctx context.Context, tenantID string) error {
				checkOrCreateCalled = true
				capturedTenantID = tenantID
				return nil
			},
		}

		// 保存原始服務並在測試後恢復
		originalSVC := service.SVC()
		service.RegisterSVC(mockSVC)
		defer func() {
			if originalSVC != nil {
				service.RegisterSVC(originalSVC)
			}
		}()

		// 直接測試服務方法
		err := service.SVC().CheckOrCreateTenant(context.Background(), "test-tenant-123")
		t.AssertNil(err)
		t.Assert(checkOrCreateCalled, true)
		t.Assert(capturedTenantID, "test-tenant-123")
	})

	gtest.C(t, func(t *gtest.T) {
		// 測試CheckOrCreateTenant返回錯誤的情況
		mockSVC := &MockSVC{
			CheckOrCreateTenantFn: func(ctx context.Context, tenantID string) error {
				return fmt.Errorf("tenant check failed")
			},
		}

		originalSVC := service.SVC()
		service.RegisterSVC(mockSVC)
		defer func() {
			if originalSVC != nil {
				service.RegisterSVC(originalSVC)
			}
		}()

		err := service.SVC().CheckOrCreateTenant(context.Background(), "test-tenant")
		t.AssertNE(err, nil)
		t.Assert(gstr.Contains(err.Error(), "tenant check failed"), true)
	})

	gtest.C(t, func(t *gtest.T) {
		// 測試空租戶ID的情況
		mockSVC := &MockSVC{
			CheckOrCreateTenantFn: func(ctx context.Context, tenantID string) error {
				if tenantID == "" {
					return fmt.Errorf("tenant ID is empty")
				}
				return nil
			},
		}

		originalSVC := service.SVC()
		service.RegisterSVC(mockSVC)
		defer func() {
			if originalSVC != nil {
				service.RegisterSVC(originalSVC)
			}
		}()

		err := service.SVC().CheckOrCreateTenant(context.Background(), "")
		t.AssertNE(err, nil)
		t.Assert(gstr.Contains(err.Error(), "tenant ID is empty"), true)
	})
}

// TestParseRequestURILogic 測試URI解析邏輯（直接測試字符串處理邏輯）
func TestParseRequestURILogic(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 直接測試URI解析邏輯，不依賴Request對象
		testCases := []struct {
			uri            string
			expectedSvc    string
			expectedAction string
			shouldError    bool
		}{
			{"/test.svc/action", "test", "action", false},
			{"/testservice/action", "testservice", "action", false},
			{"/service.svc/multiple/path/action", "service", "action", false},
			{"/invalid", "", "", true},
			{"/", "", "", true},
		}

		for _, tc := range testCases {
			// 模擬parseRequestURI的邏輯
			uris := gstr.SplitAndTrim(tc.uri, "/")
			if len(uris) <= 2 {
				if tc.shouldError {
					continue // 預期錯誤
				} else {
					t.Errorf("Expected error for URI: %s", tc.uri)
					continue
				}
			}

			action := uris[len(uris)-1]
			var svc string
			if gstr.HasSuffix(gstr.ToLower(uris[1]), ".svc") {
				svc = gstr.TrimRightStr(gstr.ToLower(uris[1]), ".svc")
			} else {
				svc = uris[1]
			}

			if tc.shouldError {
				t.Errorf("Expected error for URI: %s, but got svc=%s, action=%s", tc.uri, svc, action)
			} else {
				t.Assert(svc, tc.expectedSvc)
				t.Assert(action, tc.expectedAction)
			}
		}
	})
}
