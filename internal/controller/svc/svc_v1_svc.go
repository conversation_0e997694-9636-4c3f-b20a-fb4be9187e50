package svc

import (
	v1 "ai-gateway/api/svc/v1"
	"ai-gateway/internal/consts"
	"ai-gateway/internal/model"
	"ai-gateway/internal/service"
	"ai-gateway/utility"
	"context"
	"fmt"
	"github.com/gogf/gf/v2/net/ghttp"
	"net/http"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
)

/*
Svc
Input Parameters:
- ctx: context.Context
- _ *v1.SvcReq
Output:
- res *v1.SvcRes
- err error

This function handles a service request. It extracts information from the request, validates authorization, and sends data to a microservice. The response from the microservice is returned to the caller.
*/

func (c *ControlerV1) Svc(ctx context.Context, _ *v1.SvcReq) (res *v1.SvcRes, err error) {
	r := g.RequestFromCtx(ctx)

	// 解析請求URI
	svc, action, err := c.parseRequestURI(r)
	if err != nil {
		resp := utility.ParseRes(err)
		r.Response.WriteJson(resp)
		return
	}

	// 獲取服務配置
	out, err := c.getServiceConfig(ctx, r)
	if err != nil {
		resp := utility.ParseRes(err)
		r.Response.WriteJson(resp)
		return
	}

	g.Log().Cat(consts.DEBUG).Debugf(ctx, "The service uri:%v url:%v", out.URI, out.URL)

	// 權限驗證
	if authErr := c.validateAuthorization(ctx, r, out, svc, action); authErr != nil {
		return
	}

	// Tenant檢查
	if checkErr := c.checkTenant(ctx, r, out); checkErr != nil {
		// 只記錄日誌，不中斷請求
	}

	// 轉發請求到微服務
	response, err := c.forwardRequest(ctx, r, out)
	if err != nil {
		resp := utility.ParseRes(err)
		r.Response.WriteJson(resp)
		return
	}
	defer func() { _ = response.Close() }()

	// 處理響應
	c.handleResponse(ctx, r, response)
	return
}

// forwardRequest 轉發請求到微服務
func (c *ControlerV1) forwardRequest(ctx context.Context, r *ghttp.Request, out *model.ParseURIOutput) (*gclient.Response, error) {
	// 設置請求標頭
	c.client.SetHeaderMap(map[string]string{
		consts.HeaderAppKey:   r.GetHeader(consts.HeaderAppKey),
		consts.HeaderTenantID: r.GetHeader(consts.HeaderTenantID),
		consts.HeaderAuth:     r.GetHeader(consts.HeaderAuth),
	})

	// 檢查是否有文件上傳
	if r.MultipartForm != nil && r.MultipartForm.File != nil && len(r.MultipartForm.File) > 0 {
		return c.handleFileUpload(ctx, r, out.URL)
	} else {
		// 普通請求轉發
		return c.client.Post(ctx, out.URL, r.GetBody())
	}
}

// handleFileUpload 處理文件上傳請求
func (c *ControlerV1) handleFileUpload(ctx context.Context, r *ghttp.Request, url string) (*gclient.Response, error) {
	var tempFiles []string
	defer func() {
		// 清理臨時文件
		for _, tempFile := range tempFiles {
			if gfile.Exists(tempFile) {
				_ = gfile.RemoveFile(tempFile)
			}
		}
	}()

	// 準備表單數據
	formData := make(map[string]string)
	vParamsMap := r.GetFormMap()
	for k, v := range vParamsMap {
		formData[k] = fmt.Sprintf("%v", v)
	}

	// 處理所有上傳的文件
	for fieldName, files := range r.MultipartForm.File {
		uploadFiles := r.GetUploadFiles(fieldName)
		if uploadFiles == nil {
			continue
		}

		for i, uploadFile := range uploadFiles {
			// 生成唯一的臨時文件路徑
			tempFilePath := gfile.Join(gfile.Temp(), uploadFile.Filename)
			if gfile.Exists(tempFilePath) {
				tempFilePath = gfile.Join(gfile.Temp(), fmt.Sprintf("%d_%s", gtime.TimestampMilli(), uploadFile.Filename))
			}

			// 保存文件到臨時目錄
			_, err := uploadFile.Save(gfile.Dir(tempFilePath))
			if err != nil {
				return nil, gerror.NewCode(consts.GeneralError, fmt.Sprintf("failed to save uploaded file: %v", err))
			}

			tempFiles = append(tempFiles, tempFilePath)

			// 添加文件到表單數據
			if len(r.MultipartForm.File) == 1 && len(files) == 1 {
				formData["file"] = fmt.Sprintf("@file:%s", tempFilePath)
			} else {
				formData[fmt.Sprintf("file_%d", i)] = fmt.Sprintf("@file:%s", tempFilePath)
			}
		}
	}

	// 構建請求數據字符串
	var uploadFileParams []string
	var otherParams []string
	for k, v := range formData {
		param := fmt.Sprintf("%s=%s", k, v)
		if gstr.HasPrefix(k, "file") {
			pos := gstr.PosRune(k, "_")
			if pos != -1 {
				k = gstr.SubStrRune(k, 0, pos)
				param = fmt.Sprintf("%s=%s", k, v)
			}
			uploadFileParams = append(uploadFileParams, param)
		} else {
			otherParams = append(otherParams, param)
		}
	}

	allParams := append(uploadFileParams, otherParams...)
	requestData := gstr.Join(allParams, "&")
	g.Log().Noticef(ctx, "File upload data: %v", requestData)

	return c.client.Post(ctx, url, requestData)
}

// handleResponse 處理微服務響應
func (c *ControlerV1) handleResponse(ctx context.Context, r *ghttp.Request, response *gclient.Response) {
	// 檢查響應是否為文件下載請求
	contentDisposition := response.Header.Get("Content-Disposition")
	g.Log().Cat(consts.DEBUG).Debugf(ctx, "Content-Disposition: %s", contentDisposition)
	isFileDownload := gstr.Contains(contentDisposition, "attachment") || gstr.Contains(contentDisposition, "inline")

	if isFileDownload {
		c.handleFileDownload(ctx, r, response, contentDisposition)
	} else {
		// 對於非文件下載請求，返回JSON響應
		r.Response.WriteJson(response.ReadAllString())
	}
}

// handleFileDownload 處理文件下載響應
func (c *ControlerV1) handleFileDownload(ctx context.Context, r *ghttp.Request, response *gclient.Response, contentDisposition string) {
	g.Log().Cat(consts.DEBUG).Debugf(ctx, "Handling file download request with Content-Disposition: %s", contentDisposition)

	// 設置響應狀態碼
	r.Response.WriteStatus(response.StatusCode)

	// 轉發所有響應標頭到客戶端
	for headerName, headerValues := range response.Header {
		for _, headerValue := range headerValues {
			r.Response.Header().Add(headerName, headerValue)
		}
	}

	// 直接轉發二進制內容
	responseBody := response.ReadAll()
	fileName := extractFileNameFromContentDisposition(contentDisposition)
	if fileName == "" {
		fileName = "download_file" // 默認文件名
	}

	// 創建臨時文件
	tempFilePath := gfile.Join(gfile.Temp(), fmt.Sprintf("%d_%s", gtime.TimestampMilli(), fileName))

	// 將響應內容寫入臨時文件
	err := gfile.PutBytes(tempFilePath, responseBody)
	if err != nil {
		g.Log().Cat(consts.ERROR).Errorf(ctx, "Failed to create temp file: %v", err)
		r.Response.WriteStatus(http.StatusInternalServerError)
		return
	}

	// 確保在處理完成後清理臨時文件
	defer func() {
		if gfile.Exists(tempFilePath) {
			_ = gfile.RemoveFile(tempFilePath)
		}
	}()

	// 使用 ServeFileDownload 方法
	r.Response.ServeFileDownload(tempFilePath, fileName)

	g.Log().Cat(consts.DEBUG).Debugf(ctx, "File download response forwarded successfully, content length: %d bytes", len(responseBody))
}

// extractFileNameFromContentDisposition 從 Content-Disposition 標頭中提取文件名
func extractFileNameFromContentDisposition(contentDisposition string) string {
	if contentDisposition == "" {
		return ""
	}

	// 查找 filename= 參數
	if pos := gstr.Pos(contentDisposition, "filename="); pos != -1 {
		filename := gstr.SubStr(contentDisposition, pos+9) // "filename=" 長度為 9

		// 移除引號
		filename = gstr.Trim(filename, "\"")
		filename = gstr.Trim(filename, "'")

		// 如果有分號，只取分號前的部分
		if pos := gstr.Pos(filename, ";"); pos != -1 {
			filename = gstr.SubStr(filename, 0, pos)
		}

		return gstr.Trim(filename)
	}

	return ""
}

// parseRequestURI 解析請求URI，提取服務名稱和動作
func (c *ControlerV1) parseRequestURI(r *ghttp.Request) (svc, action string, err error) {
	uris := gstr.SplitAndTrim(r.RequestURI, "/")
	if len(uris) <= 2 {
		err = gerror.NewCode(consts.BadRequestUri, "Invalid URI format")
		return
	}

	action = uris[len(uris)-1]
	if gstr.HasSuffix(gstr.ToLower(uris[1]), ".svc") {
		svc = gstr.TrimRightStr(gstr.ToLower(uris[1]), ".svc")
	} else {
		svc = uris[1]
	}

	return
}

// getServiceConfig 獲取服務配置
func (c *ControlerV1) getServiceConfig(ctx context.Context, r *ghttp.Request) (*model.ParseURIOutput, error) {
	uris := gstr.SplitAndTrim(r.RequestURI, "/")
	return service.SVC().ReplaceToInstanceUri(
		ctx,
		model.ParseURIInput{
			RequestURI: gstr.Join(uris[1:], "/"),
		},
	)
}

// validateAuthorization 驗證權限
func (c *ControlerV1) validateAuthorization(ctx context.Context, r *ghttp.Request, out *model.ParseURIOutput, svc, action string) error {
	if !out.Authorized {
		return nil
	}

	tenantId := ""
	if gjson.Valid(r.GetBodyString()) {
		tenantId = gjson.New(r.GetBodyString()).Get(consts.HeaderTenantID, "").String()
	}

	result, err := service.SVC().ValidateTenant(
		ctx,
		model.ValidateTenantInput{
			TenantID: fmt.Sprintf("%s^%s", r.GetHeader(consts.HeaderTenantID), tenantId),
			AppKey:   r.GetHeader(consts.HeaderAppKey),
			Svc:      svc,
			Token:    r.GetHeader(consts.HeaderAuth),
			Action:   action,
		},
	)

	if err != nil || !result {
		g.Log().Cat(consts.ERROR).Debug(ctx, "The current tenant does not have permission")
		r.Response.WriteStatus(http.StatusUnauthorized)
		return gerror.NewCode(consts.TenantIllegal, "Unauthorized")
	}

	return nil
}

// checkTenant 檢查租戶
func (c *ControlerV1) checkTenant(ctx context.Context, r *ghttp.Request, out *model.ParseURIOutput) error {
	if !out.Check {
		return nil
	}
	tenantId := ""
	if gjson.Valid(r.GetBodyString()) {
		tenantId = gjson.New(r.GetBodyString()).Get(consts.HeaderTenantID, "").String()
	}

	// 檢查tenant是否存在，如果不存在則創建並創建關聯表
	if err := service.SVC().CheckOrCreateTenant(ctx, tenantId); err != nil {
		g.Log().Cat(consts.ERROR).Errorf(ctx, "Failed to check or create tenant %s: %v", tenantId, err)
		return err
	}

	return nil
}
