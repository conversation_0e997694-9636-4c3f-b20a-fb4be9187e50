package svc

import (
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
	"github.com/gogf/gf/v2/text/gstr"
)

// TestFileDownloadDetection 測試文件下載請求檢測邏輯
func TestFileDownloadDetection(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 測試 attachment 類型
		contentDisposition1 := "attachment; filename=\"test.pdf\""
		isFileDownload1 := gstr.Contains(contentDisposition1, "attachment") || gstr.Contains(contentDisposition1, "inline")
		t.Assert(isFileDownload1, true)

		// 測試 inline 類型
		contentDisposition2 := "inline; filename=\"image.jpg\""
		isFileDownload2 := gstr.Contains(contentDisposition2, "attachment") || gstr.Contains(contentDisposition2, "inline")
		t.Assert(isFileDownload2, true)

		// 測試空字符串
		contentDisposition3 := ""
		isFileDownload3 := gstr.Contains(contentDisposition3, "attachment") || gstr.Contains(contentDisposition3, "inline")
		t.Assert(isFileDownload3, false)

		// 測試其他類型
		contentDisposition4 := "form-data; name=\"field\""
		isFileDownload4 := gstr.Contains(contentDisposition4, "attachment") || gstr.Contains(contentDisposition4, "inline")
		t.Assert(isFileDownload4, false)

		// 測試包含 attachment 的其他情況
		contentDisposition5 := "attachment"
		isFileDownload5 := gstr.Contains(contentDisposition5, "attachment") || gstr.Contains(contentDisposition5, "inline")
		t.Assert(isFileDownload5, true)

		// 測試包含 inline 的其他情況
		contentDisposition6 := "inline"
		isFileDownload6 := gstr.Contains(contentDisposition6, "attachment") || gstr.Contains(contentDisposition6, "inline")
		t.Assert(isFileDownload6, true)
	})
}
