package model

import (
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestCheckOrCreateTenantInput 測試CheckOrCreateTenantInput結構體
func TestCheckOrCreateTenantInput(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		input := CheckOrCreateTenantInput{
			TenantID: "test-tenant-123",
		}
		
		t.<PERSON><PERSON><PERSON>(input.TenantID, "test-tenant-123")
	})

	gtest.C(t, func(t *gtest.T) {
		// 測試空值
		input := CheckOrCreateTenantInput{}
		t.Assert(input.TenantID, "")
	})
}

// TestCheckOrCreateTenantReq 測試CheckOrCreateTenantReq結構體
func TestCheckOrCreateTenantReq(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		req := CheckOrCreateTenantReq{
			TenantID: "test-tenant-456",
		}
		
		t.Asser<PERSON>(req.TenantID, "test-tenant-456")
	})

	gtest.C(t, func(t *gtest.T) {
		// 測試JSON標籤
		req := CheckOrCreateTenantReq{
			TenantID: "json-test",
		}
		
		// 驗證結構體字段
		t.Assert(req.TenantID, "json-test")
	})
}

// TestCheckOrCreateTenantRes 測試CheckOrCreateTenantRes結構體
func TestCheckOrCreateTenantRes(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		res := CheckOrCreateTenantRes{
			Code:    0,
			Message: "success",
		}
		
		t.Assert(res.Code, 0)
		t.Assert(res.Message, "success")
	})

	gtest.C(t, func(t *gtest.T) {
		// 測試錯誤響應
		res := CheckOrCreateTenantRes{
			Code:    -1,
			Message: "tenant creation failed",
		}
		
		t.Assert(res.Code, -1)
		t.Assert(res.Message, "tenant creation failed")
	})

	gtest.C(t, func(t *gtest.T) {
		// 測試默認值
		res := CheckOrCreateTenantRes{}
		t.Assert(res.Code, 0)
		t.Assert(res.Message, "")
	})
}

// TestValidateTenantInput 測試現有的ValidateTenantInput結構體（確保沒有被破壞）
func TestValidateTenantInput(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		input := ValidateTenantInput{
			TenantID: "test-tenant",
			AppKey:   "test-app-key",
			Svc:      "test-service",
			Token:    "test-token",
			Action:   "test-action",
		}
		
		t.Assert(input.TenantID, "test-tenant")
		t.Assert(input.AppKey, "test-app-key")
		t.Assert(input.Svc, "test-service")
		t.Assert(input.Token, "test-token")
		t.Assert(input.Action, "test-action")
	})
}

// TestParseURIInput 測試現有的ParseURIInput結構體（確保沒有被破壞）
func TestParseURIInput(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		input := ParseURIInput{
			RequestURI: "test/service/action",
		}
		
		t.Assert(input.RequestURI, "test/service/action")
	})
}

// TestParseURIOutput 測試現有的ParseURIOutput結構體（確保沒有被破壞）
func TestParseURIOutput(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		output := ParseURIOutput{
			URI:        "/v1/test/action",
			URL:        "http://test.svc/v1/test/action",
			Authorized: true,
			Check:      true,
		}
		
		t.Assert(output.URI, "/v1/test/action")
		t.Assert(output.URL, "http://test.svc/v1/test/action")
		t.Assert(output.Authorized, true)
		t.Assert(output.Check, true)
	})
}

// TestServicesUri 測試現有的ServicesUri結構體（確保沒有被破壞）
func TestServicesUri(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		uri := ServicesUri{
			Id:         "test.svc",
			Path:       "/test/*",
			Uri:        "/v1/test/",
			Scheme:     "http",
			Authorized: true,
			Service:    "test.svc",
			Check:      true,
		}
		
		t.Assert(uri.Id, "test.svc")
		t.Assert(uri.Path, "/test/*")
		t.Assert(uri.Uri, "/v1/test/")
		t.Assert(uri.Scheme, "http")
		t.Assert(uri.Authorized, true)
		t.Assert(uri.Service, "test.svc")
		t.Assert(uri.Check, true)
	})

	gtest.C(t, func(t *gtest.T) {
		// 測試ParseUri方法
		uri := ServicesUri{
			Path: "/test/*",
			Uri:  "/v1/test/",
		}
		
		err, newUri := uri.ParseUri("/test/*", "action")
		t.AssertNil(err)
		t.Assert(newUri, "/v1/test/action")
	})
}
