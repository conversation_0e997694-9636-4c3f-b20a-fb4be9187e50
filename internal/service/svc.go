// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"ai-gateway/internal/model"
	"context"
)

type (
	ISVC interface {
		ReplaceToInstanceUri(ctx context.Context, in model.ParseURIInput) (out *model.ParseURIOutput, err error)
		ValidateTenant(ctx context.Context, in model.ValidateTenantInput) (passed bool, err error)
		CheckOrCreateTenant(ctx context.Context, tenantID string) error
	}
)

var (
	localSVC ISVC
)

func SVC() ISVC {
	if localSVC == nil {
		panic("implement not found for interface ISVC, forgot register?")
	}
	return localSVC
}

func RegisterSVC(i ISVC) {
	localSVC = i
}
