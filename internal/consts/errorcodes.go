package consts

import "github.com/gogf/gf/v2/errors/gcode"

// 定義 error code

// 定義 ai-gateway error codes
// 範圍: -2 ~ -?? (-1 -> CodeNil)

var (
	Success              = gcode.New(0, "Execution succeed", nil)
	GeneralError         = gcode.New(-1, "", nil)
	BadRequestUri        = gcode.New(-2, `The format of uri is incorrect. Correct format: /service name/resource name/action`, nil)
	ServiceIsNotExist    = gcode.New(-3, `The service is not exist`, nil)
	KeyIsNotExist        = gcode.New(-4, `The "Tenant_ID" or "App_Key" is not existed`, nil)
	GetMicroServiceError = gcode.New(-5, "Get service instance failed", nil)
	PostRequestFailed    = gcode.New(-6, "Failed to post content", nil)
	NacosGeneralError    = gcode.New(-7, "Nacos general error", nil)
	TenantIllegal        = gcode.New(-8, "Illegal tenant", nil)
	NotInitialize        = gcode.New(-9, `Initialize error`, nil)
	ParamsError          = gcode.New(-10, `Params error`, nil)
	SubscriberIsNotFound = gcode.New(-11, `Subscriber is not found`, nil)
)
