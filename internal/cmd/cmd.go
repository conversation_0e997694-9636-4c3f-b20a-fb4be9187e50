package cmd

import (
	"ai-gateway/internal/consts"
	"ai-gateway/internal/controller/svc"
	"context"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcmd"
)

var (
	Main = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "start http server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			s := g.Server(consts.ServiceName)

			s.Group("/", func(group *ghttp.RouterGroup) {
				group.Middleware(MiddleHandler, ghttp.MiddlewareHandlerResponse)
				group.Bind(svc.NewV1())
			})

			s.Run()

			return nil
		},
	}
)

// MiddleHandler 中间件拦截处理。 将请求转发给指定的服务。 并将返回值再返回
func MiddleHandler(r *ghttp.Request) {
	// 拦截处理所有的request 请求在中间件中处理
	ctx := r.GetCtx()
	r.Response.CORSDefault()
	if gjson.Valid(r.GetBodyString()) {
		jsReq := gjson.New(r.GetBodyString())
		if jsReq.Contains("vector") {
			_ = jsReq.Set("vector", "***")
		}
		strReq := jsReq.MustToJsonIndentString()
		g.Log().Cat(consts.RR).Debugf(ctx, "Request-Uri: %s Body:%s", r.RequestURI, strReq)

	} else {
		g.Log().Cat(consts.RR).Debugf(ctx, "Request-Uri: %s ", r.RequestURI)

	}
	r.Middleware.Next()

	if gjson.Valid(r.Response.BufferString()) {
		jsResp := gjson.New(r.Response.BufferString())
		if jsResp.Contains("vector") {
			_ = jsResp.Set("vector", "***")
		}
		strResp := jsResp.MustToJsonIndentString()
		g.Log().Cat(consts.RR).Debugf(ctx, "Response-%s", strResp)
	} else {
		g.Log().Cat(consts.RR).Debug(ctx, "Return response")

	}

}
